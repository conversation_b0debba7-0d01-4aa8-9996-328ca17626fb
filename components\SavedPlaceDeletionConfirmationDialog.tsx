import * as React from 'react';
import { StyleSheet } from "react-native";
import { <PERSON>ton, Dialog, Portal, Text } from 'react-native-paper';
import { apiServiceContext, translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';
import BasicInfoDialog from './BasicInfoDialog';
import { boolean } from 'yup';
import { CustomPlace } from '../models/place';
import { ApiService } from '../services/api/apiService';
import useGlobalStore from '../services/globalState';

interface Props {
    readonly visible: boolean;
    readonly savedPlace: CustomPlace;
    readonly postDeletion: () => void;
}

const SavedPlaceDeletionConfirmationDialog: React.FC<Props> = ({ visible, savedPlace, postDeletion }) => {

    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);
    const apiService: ApiService = React.useContext<ApiService>(apiServiceContext);

    const deleteSavedPlace = useGlobalStore((store) => store.deleteSavedPlace);

    const [dialogVisible, setDialogVisible] = React.useState<boolean>(visible);
    const [deleting, setDeleting] = React.useState<boolean>(false);

    const onDelete = () => {
        setDeleting(true);
        await apiService.deleteUserSavedPlace(userUid, savedPlace.placeId);
        deleteSavedPlace(savedPlace.placeId);
    };

    return (
        <BasicInfoDialog
            visible={visible}
            icon="map-search"
            title={translationService.translate("SAVED_PLACE_DELETION_CONFIRMATION_DIALOG_TITLE")}
            content={translationService.translate("SAVED_PLACE_DELETION_CONFIRMATION_DIALOG_CONTENT")}
            mainActionButtonLabel={translationService.translate("DELETE")}
            onDismiss={() => setDialogVisible(false)}
            onMainAction={() => onDelete()}
        />
    );
};

const styles = StyleSheet.create({
});


export default SavedPlaceDeletionConfirmationDialog;