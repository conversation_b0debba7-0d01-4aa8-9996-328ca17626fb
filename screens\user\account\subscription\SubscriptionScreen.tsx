import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import React, { useContext } from "react";
import { ScrollView, StyleSheet, View } from 'react-native';
import { Divider, Icon, Text } from "react-native-paper";
import ReadonlyInfo from "../../../../components/ReadonlyInfo";
import SubscribeButton from '../../../../components/profile/SubscribeButton';
import { SubscriptionPeriod } from "../../../../models/types";
import { UserInfo } from "../../../../models/userDetails";
import { authSelectors } from "../../../../services/authService";
import useGlobalStore from "../../../../services/globalState";
import { translationServiceContext, whatsappServiceContext } from "../../../../services/provider";
import { TranslationService } from "../../../../services/translationService";
import { WhatsappService } from '../../../../services/whatsappService';
import { usePurchaseFlow } from '../../../../hooks/usePurchaseFlow';

type SubscriptionScreenProps = NativeStackScreenProps<MainUserStackParamList, 'Subscription'>;

const SubscriptionScreen: React.FC<SubscriptionScreenProps> = ({ navigation, route }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const whatsappService: WhatsappService = useContext<WhatsappService>(whatsappServiceContext);

    const userDetails: UserInfo = useGlobalStore(authSelectors.authenticatedUserDetails);

    const {
        findCurrentOrLatestPastPeriodByType,
        isUserSubscribed
    } = useGlobalStore();

    const { startPurchaseFlow } = usePurchaseFlow();

    const handleSubscribe = () => {
        startPurchaseFlow(userDetails.uid);
    };

    const currentTime: Date = new Date();
    const currentOrLatestPastSubscriptionPeriod: SubscriptionPeriod | null = findCurrentOrLatestPastPeriodByType(currentTime, 'SUBSCRIPTION');

    const subscriptionStatus: string = (() => {
        if (!currentOrLatestPastSubscriptionPeriod) return "--";
        return isUserSubscribed(currentTime)
            ? translationService.translate("ACTIVE")
            : translationService.translate("EXPIRED");
    })();

    const formatDate = (date: Date | undefined): string => {
        if (!date) return "--";
        return format(date, 'Pp', { locale: fr });
    };

    return (
        <ScrollView>
            <View>
                {currentOrLatestPastSubscriptionPeriod ? (
                    <View style={styles.formContainer}>
                        <ReadonlyInfo
                            label={translationService.translate("SUBSCRIPTION_PLAN_TYPE")}
                            value={currentOrLatestPastSubscriptionPeriod?.plan ? translationService.translate(currentOrLatestPastSubscriptionPeriod.plan) : "--"}
                        />
                        <Divider />
                        <ReadonlyInfo
                            label={translationService.translate("SUBSCRIPTION_STATUS")}
                            value={subscriptionStatus}
                        />
                        <Divider />
                        <ReadonlyInfo
                            label={translationService.translate("SUBSCRIPTION_START_DATE")}
                            value={formatDate(currentOrLatestPastSubscriptionPeriod?.startDate)}
                        />
                        <Divider />
                        <ReadonlyInfo
                            label={translationService.translate("SUBSCRIPTION_END_DATE")}
                            value={formatDate(currentOrLatestPastSubscriptionPeriod?.endDate)}
                        />
                        <Divider />
                        <ReadonlyInfo
                            label={translationService.translate("SUBSCRIPTION_AUTO_RENEW")}
                            value={translationService.translate("NO")}
                        />
                        {/* <View style={{ height: 12 }} />
                            <Button
                                mode="outlined"
                                compact={true}
                                onPress={() => {
                                    revenueCatService.restorePurchases();
                                }}
                                style={{ alignSelf: 'flex-start' }}
                            >
                                {translationService.translate("SUBSCRIPTION_RESTORE_PURCHASES")}
                            </Button> */}
                    </View>
                ) : (
                    <View style={styles.emptyListContainer}>
                        <View style={styles.fadedContent}>
                            <Icon source="star-off-outline" size={100} />
                            <Text variant="bodyLarge" style={styles.textAlignCenter}>
                                {translationService.translate("NO_ACTIVE_SUBSCRIPTION")}
                            </Text>
                            <Text variant="bodyMedium" style={styles.textAlignCenter}>
                                {translationService.translate("SUBSCRIBE_TO_ACCESS_PREMIUM")}
                            </Text>
                        </View>
                        <SubscribeButton
                            icon={'credit-card-outline'}
                            onPress={handleSubscribe}
                            style={styles.subscribeButton}
                        />
                    </View>
                )}

            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    formContainer: {
        flexDirection: 'column',
        gap: 8,
        padding: 16,
    },
    emptyListContainer: {
        marginTop: 24,
        padding: 24,
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        gap: 16
    },
    fadedContent: {
        opacity: 0.7,
        alignItems: 'center',
        gap: 16
    },
    textAlignCenter: {
        textAlign: 'center'
    },
    subscribeButton: {
        marginTop: 16,
    },
    underline: {
        textDecorationLine: 'underline'
    },
    bold: {
        fontWeight: 'bold'
    },
    profileHeaderContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 24,
        gap: 4,
    },
    profileSubHeaderContainer: {
        alignItems: 'center',
        marginBottom: 16,
    },
    descriptionView: {
        flex: 1,
        gap: 8,
    },
    placeType: {
        fontWeight: 'bold',
    },
    itemPadding: {
        paddingHorizontal: 8
    },
    signOutTextColor: {
        color: '#D32F2F'
    },
    avatarContainer: {
        position: 'relative',
        marginBottom: 8,
    },
    badge: {
        position: 'absolute',
        bottom: -5,
        right: -5,
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 12,
        borderWidth: 2,
        zIndex: 1,
    },
    badgeText: {
        fontWeight: 'bold',
        fontSize: 12,
    },
    loadingDialogContent: {
        alignItems: 'center',
        padding: 16,
    },
    loadingText: {
        marginTop: 16,
        textAlign: 'center',
    },
});

export default SubscriptionScreen;
