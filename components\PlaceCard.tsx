import React, { useContext, useState } from 'react';
import { Image, Linking, StyleSheet } from "react-native";
import { <PERSON><PERSON>, <PERSON>, Divider, IconButton, MD3Theme, Text, useTheme } from 'react-native-paper';
import { PlaceType, PlaceTypeParams, placeTypeConf } from "../models/constants";
import { CustomPlace, Place } from "../models/place";
import { generateGoogleMapsURL, generateLatLng } from "../utils/others";
import { TranslationService } from '../services/translationService';
import { translationServiceContext } from '../services/provider';

interface Props {
    readonly place: Place | CustomPlace;
    readonly deleting: boolean;
    readonly onDetailsPress: () => void;
    readonly onDismiss: () => void;
    readonly onDelete: () => void;
    readonly onHidePharmacyPerimeterPress: () => Promise<void>;
    readonly onShowPharmacyPerimeterPress: () => Promise<void>;
}

const PlaceCard: React.FC<Props> = ({ place, deleting, onDelete, onDismiss, onDetailsPress, onHidePharmacyPerimeterPress, onShowPharmacyPerimeterPress }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const theme: MD3Theme = useTheme();

    const placeTypeParams: PlaceTypeParams = placeTypeConf[place.type];

    const [loading, setLoading] = useState<boolean>(false);

    const hidePharmacyPerimeter = async () => {
        setLoading(true);
        try {
            return await onHidePharmacyPerimeterPress();
        } finally {
            setLoading(false);
        }
    };

    const showPharmacyPerimeter = async () => {
        setLoading(true);
        try {
            return await onShowPharmacyPerimeterPress();
        } finally {
            setLoading(false);
        }
    };

    const onGoogleMapsPress = () => {
        const googleMapsURL: string = place.googleMapsUri ?? generateGoogleMapsURL(place.geometry.lat, place.geometry.lng, place.placeId);
        Linking.openURL(googleMapsURL);
    };

    const isHiddenExistingPharmacy = PlaceType.EXISTING_PHARMACY === place.type && place.tags?.includes('HIDDEN');
    const isNotAHiddenExistingPharmacy = PlaceType.EXISTING_PHARMACY === place.type && !place.tags?.includes('HIDDEN');

    console.log('isHiddenExistingPharmacy', isHiddenExistingPharmacy);
    console.log('isNotAHiddenExistingPharmacy', isNotAHiddenExistingPharmacy);
    console.log('loading', loading);

    return (
        <Card style={styles.card}>
            <Card.Title
                title={place.name}
                titleVariant="titleLarge"
                titleNumberOfLines={2}
                subtitle={generateLatLng(place.geometry.lat, place.geometry.lng)}
                subtitleStyle={{
                    color: theme.colors.secondary
                }}
                rightStyle={styles.cardTitleRight}
                right={({ }) => (
                    <IconButton
                        icon="close"
                        mode="contained"
                        size={18}
                        onPress={onDismiss}
                    />
                )}
            />
            <Card.Content style={styles.cardContent}>
                <Text variant="titleMedium" style={{ color: placeTypeParams.style.color }}>
                    {placeTypeParams.label}
                </Text>
                {PlaceType.EXISTING_PHARMACY === place.type && (
                    <Text variant="bodySmall" style={{ color: "gray" }}>
                        ({translationService.translate("FOUND_ON_GOOGLE_MAPS")})
                    </Text>
                )}
            </Card.Content>
            <Divider />
            <Card.Actions style={styles.cardActions}>
                {PlaceType.EXISTING_PHARMACY === place.type && (
                    <Button
                        mode="outlined"
                        disabled={loading}
                        loading={loading}
                        icon={isHiddenExistingPharmacy ? "crosshairs" : "crosshairs-off"}
                        onPress={isHiddenExistingPharmacy ? showPharmacyPerimeter : hidePharmacyPerimeter}
                        style={styles.flexGrow}
                    >
                        {isHiddenExistingPharmacy ? translationService.translate("PERIMETER_SHOW") : translationService.translate("PERIMETER_HIDE")}
                    </Button>
                )}
                {(PlaceType.CHAINAGE === place.type || PlaceType.PHARMACY === place.type) && (
                    <Button
                        icon="format-list-bulleted"
                        mode="outlined"
                        onPress={onDetailsPress}
                        style={styles.flexGrow}
                    >
                        {translationService.translate("MORE_DETAILS")}
                    </Button>
                )}
                <IconButton
                    mode="outlined"
                    icon={({ size }) => (
                        <Image
                            source={require('../assets/images/google-maps.png')}
                            style={{ width: size, height: size, }}
                        />
                    )}
                    onPress={onGoogleMapsPress}
                />
                {(PlaceType.CHAINAGE === place.type || PlaceType.PHARMACY === place.type) && (
                    <IconButton
                        icon="delete"
                        iconColor="red"
                        mode="outlined"
                        disabled={deleting}
                        onPress={onDelete}
                    />
                )}

            </Card.Actions>
        </Card>
    );
};

const styles = StyleSheet.create({
    flexGrow: {
        flexGrow: 1
    },
    card: {
    },
    cardTitleRight: {
        padding: 8,
        marginTop: 0,
        paddingTop: 0,
    },
    cardContent: {
        marginBottom: 16
    },
    cardActions: {
    }
});

export default PlaceCard;
