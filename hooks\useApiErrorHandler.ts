import { useCallback, useContext } from "react";
import { InternetConnectionError } from "../errors/InternetConnectionError";
import useGlobalStore from "../services/globalState";
import { translationServiceContext } from "../services/provider";
import { TranslationService } from "../services/translationService";

export function useApiErrorHandler() {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const showSnackbar = useGlobalStore((state) => state.showSnackbar);

    const handleApiError = useCallback((error: unknown, errorCode?: string) => {
        if (error instanceof InternetConnectionError) {
            showSnackbar(translationService.translate('NO_INTERNET_CONNECTION'), 'error');
            return;
        } else if (errorCode) {
            showSnackbar(translationService.translate(errorCode), 'error');
        } else {
            showSnackbar(translationService.translate('UNKNOWN_ERROR'), 'error');
        }
    }, []);

    return {
        handleApiError
    };
}
