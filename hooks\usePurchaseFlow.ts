// usePurchaseFlow.ts

import { useContext } from "react";
import { ApiService } from "../services/api/apiService";
import useGlobalStore from "../services/globalState";
import { apiServiceContext, revenueCatServiceContext } from "../services/provider";
import { RevenueCatService } from "../services/revenueCatService";
import { PAYWALL_RESULT } from "react-native-purchases-ui";
import { Alert } from "react-native";
import { UserDetails } from "../services/api/models/userDetails";

export const usePurchaseFlow = () => {

    const apiService: ApiService = useContext(apiServiceContext);
    const revenueCatService: RevenueCatService = useContext(revenueCatServiceContext);

    const {
        setCheckingPurchase,
        setSubscriptionSucessVisible,
        update,
        isUserSubscribed
    } = useGlobalStore();

    const refreshUserData = async (userUid: string): Promise<void> => {
        const response = await apiService.getUserDetails(userUid);
        if (response.ok) {
            const userDetails: UserDetails = await response.json();
            update(userDetails);
        } else {
            throw new Error("Failed to fetch user details");
        }
    };

    const hasActiveEntitlement = async (): Promise<boolean> => {
        try {
            const revenueCatSubscription = await revenueCatService.hasActiveEntitlement();
            return revenueCatSubscription.active;
        } catch (error) {
            console.error('[PurchaseFlow] Failed to verify RevenueCat subscription:', error);
            return false;
        }
    };

    const waitForSubscriptionConfirmationFromBackend = async (userUid: string, maxAttempts: number = 5, initialDelay: number = 1000): Promise<boolean> => {
        const checkSubscription = async (attempt: number): Promise<boolean> => {
            try {
                const revenueCatActive = await hasActiveEntitlement();
                if (!revenueCatActive) {
                    console.warn('[PurchaseFlow] Subscription not found in RevenueCat');
                    return false;
                }

                await refreshUserData(userUid);
                const isSubscribed: boolean = isUserSubscribed(new Date());

                // After refreshing user data, the UI components using currentTime 
                // aren't immediately updated

                if (isSubscribed) {
                    return true;
                }

                if (attempt >= maxAttempts) {
                    console.warn('[PurchaseFlow] Backend subscription sync failed after maximum attempts');
                    return false;
                }

                const delay = initialDelay * Math.pow(2, attempt - 1);
                await new Promise(resolve => setTimeout(resolve, delay));
                return checkSubscription(attempt + 1);
            } catch (error) {
                console.error('[PurchaseFlow] Error during subscription verification:', error);
                return false;
            }
        };

        return checkSubscription(1);
    };

    const startPurchaseFlow = async (userUid: string) => {
        try {
            await revenueCatService.login(userUid);
            const paywallResult: PAYWALL_RESULT | null = await revenueCatService.presentPaywall();
            const purchasedOrRestored: boolean = paywallResult === PAYWALL_RESULT.PURCHASED || paywallResult === PAYWALL_RESULT.RESTORED;

            if (!purchasedOrRestored) {
                console.debug('[PurchaseFlow] User cancelled or purchase failed');
                setCheckingPurchase(false);
            } else if (PAYWALL_RESULT.RESTORED === paywallResult) {
                setCheckingPurchase(true);
                console.debug('[PurchaseFlow] Subscription restored');
                const activeEntitlement: boolean = await hasActiveEntitlement();
                if (activeEntitlement) {
                    const subscriptionConfirmedFromBackend: boolean = await waitForSubscriptionConfirmationFromBackend(userUid);
                    if (subscriptionConfirmedFromBackend) {
                        setSubscriptionSucessVisible(true);
                    } else {
                        console.error('[PurchaseFlow] Purchase restored but subscription verification failed from backend');
                        setCheckingPurchase(false);
                        Alert.alert(
                            "Problème de synchronisation",
                            "Votre abonnement a été restauré, mais la mise à jour de votre statut a échoué. Veuillez redémarrer l'application ou contacter le support si le problème persiste."
                        );
                    }
                } else {
                    setCheckingPurchase(false);
                    // Here you might want to show an error dialog or trigger support contact
                    Alert.alert("Aucune offre active", "Aucun abonnement actif trouvé à restaurer.");
                }
            } else if (PAYWALL_RESULT.PURCHASED === paywallResult) {
                setCheckingPurchase(true);
                console.debug('[PurchaseFlow] Subscription purchased');
                const subscriptionConfirmedFromBackend: boolean = await waitForSubscriptionConfirmationFromBackend(userUid);
                if (subscriptionConfirmedFromBackend) {
                    setSubscriptionSucessVisible(true);
                } else {
                    // Show error dialog to inform the user
                    console.error('[PurchaseFlow] Purchase completed but subscription verification failed');
                    setCheckingPurchase(false);
                    Alert.alert(
                        "Problème de synchronisation",
                        "Votre achat a été traité, mais la mise à jour de votre statut a échoué. Veuillez redémarrer l'application ou contacter le support si le problème persiste."
                    );
                }
            } else {
                console.error('[PurchaseFlow] Unexpected paywall result:', paywallResult);
            }
        } catch (error) {
            console.error('[PurchaseFlow] Purchase flow failed:', error);
        } finally {
            setCheckingPurchase(false);
        }
    };

    return { startPurchaseFlow };
};

